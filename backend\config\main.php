<?php
$params = array_merge(
    require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-local.php',
    require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php'
);

return [
    'id' => 'app-backend',
    'basePath' => dirname(__DIR__),
    'controllerNamespace' => 'backend\controllers',
    'bootstrap' => ['log'],
    'modules' => [
        'gridview' =>  [
            'class' => '\kartik\grid\Module'
        ],
        'audit' => [
            'class' => bedezign\yii2\audit\Audit::class,
            'accessRoles' => ['@'], // restrict to logged-in users, or use ['admin'] if you want only admins
            // Optional: customize settings
            'ignoreActions' => [
                'debug/*',
                'audit/*',
            ],
            // 'panels' => [
            //     // Add or remove panels
            //     'audit/log' => [
            //         'class' => bedezign\yii2\audit\panels\LogPanel::class,
            //     ],
            // ],
        ],
    ],
    'components' => [
        'i18n' => [
            'translations' => [
                'yii2-ajaxcrud' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    'basePath' => '@yii2ajaxcrud/ajaxcrud/messages',
                    'sourceLanguage' => 'en',
                ],
            ]
        ],
        'request' => [
            'csrfParam' => '_csrf-backend',
        ],
        'user' => [
            'identityClass' => 'common\models\User',
            'enableAutoLogin' => true,
            'identityCookie' => ['name' => '_identity-backend', 'httpOnly' => true],
            'loginUrl' => ['site/login'], // default login route
        ],
        'session' => [
            // this is the name of the session cookie used for login on the backend
            'name' => 'advanced-backend',
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => \yii\log\FileTarget::class,
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'urlManager' => [
            'enablePrettyUrl' => false,
            'showScriptName' => false,
            'rules' => [
                '<controller:\w+>/<action:\w+>' => '<controller>/<action>',
            ],
        ],
        'breadcrumbsHelper' => [
            'class' => 'common\components\BreadcrumbsHelper',
            // 'map' => [...] // optionally override labels per route
        ],
    ],
    'params' => $params,
    'as breadcrumbBehavior' => [
        'class' => 'common\components\BreadcrumbBehavior',
    ],
    'as notificationListener' => [
        'class' => \common\components\NotificationListener::class,
    ],
];
