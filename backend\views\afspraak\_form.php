<?php

use kartik\form\ActiveForm;
use kartik\time\TimePicker;
use yii\helpers\Html;

/* @var $this yii\web\View */
/* @var $model common\models\Booking */
/* @var $form yii\widgets\ActiveForm */
?>


<div class="booking-form">
    <?php $form = ActiveForm::begin(['id' => 'booking-form']); ?>

    <?= $form->field($model, 'title')->textInput() ?>
    <?= $form->field($model, 'date')->textInput(['id' => 'booking-date', 'readonly' => true]) ?>
    <?= $form->field($model, 'description')->textarea() ?>

    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'start_time')->widget(TimePicker::class, [
                'pluginOptions' => [
                    'showMeridian' => false,
                    'minuteStep' => 30,
                    'defaultTime' => '10:00',
                ],
                'id' => 'afspraak-start_time'
            ]); ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'end_time')->widget(TimePicker::class, [
                'pluginOptions' => [
                    'showMeridian' => false,
                    'minuteStep' => 30,
                    'defaultTime' => '10:30',
                ],
                'id' => 'afspraak-end_time'
            ]); ?>
        </div>
    </div>

    <?php if (!Yii::$app->request->isAjax) { ?>
        <div class="form-group">
            <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
        </div>
    <?php } ?>

    <?php ActiveForm::end(); ?>
</div>

<?php
$this->registerJs("
    // Ensure the date gets set when the form is loaded
    $(document).ready(function() {
        var selectedDate = sessionStorage.getItem('selectedDate');
        if (selectedDate) {
            $('#booking-date').val(selectedDate);
        }
    });

    $('#afspraak-start_time').on('change', function() {
        let start = $(this).val();
        if (start) {
            let [hours, minutes] = start.split(':').map(Number);
            let endDate = new Date();
            endDate.setHours(hours);
            endDate.setMinutes(minutes + 30);

            let endHours = String(endDate.getHours()).padStart(2, '0');
            let endMinutes = String(endDate.getMinutes()).padStart(2, '0');

            let newEndTime = endHours + ':' + endMinutes;
            $('#afspraak-end_time').val(newEndTime).trigger('change');
        }
    });
", \yii\web\View::POS_READY);
?>

<?php
$script = <<<JS
// Add form submit handler
$('#booking-form').on('beforeSubmit', function(e) {
    var form = $(this);
    var submitBtn = form.find(':submit');
    submitBtn.prop('disabled', true);
    
    $.ajax({
        url: form.attr('action'),
        type: 'POST',
        data: form.serialize(),
        success: function(response) {
            if (response.forceReload) {
                // Reload the calendar instead of datatable
           
                // Always force-close the modal
            var modalInstance = bootstrap.Modal.getInstance(modalEl);
            modalInstance.hide();


                // Optional: refetch FullCalendar events
                $('.fc-view-container').fullCalendar('refetchEvents');

                // Toast message
                Toast.fire({
                    icon: 'success',
                    title: 'Afspraak created successfully!'
                });
            } else {
                $('#ajaxCrudModal').find('.modal-content').html(response.content);
            }
        },
        error: function() {
            Toast.fire({
                icon: 'error',
                title: 'An error occurred while saving the appointment.'
            });
        },
        complete: function() {
            submitBtn.prop('disabled', false);
        }
    });
    return false;
});
JS;
$this->registerJs($script);
?>